'use client'

import React from 'react'
import './PayloadUIComponents.scss'

// PayloadCMS-compatible Card component
export const PayloadCard: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <div className={`payload-card ${className}`}>
    {children}
  </div>
)

export const PayloadCardHeader: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <div className={`payload-card__header ${className}`}>
    {children}
  </div>
)

export const PayloadCardContent: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <div className={`payload-card__content ${className}`}>
    {children}
  </div>
)

export const PayloadCardTitle: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <h3 className={`payload-card__title ${className}`}>
    {children}
  </h3>
)

export const PayloadCardDescription: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <p className={`payload-card__description ${className}`}>
    {children}
  </p>
)

// PayloadCMS-compatible Badge component
export const PayloadBadge: React.FC<{
  children: React.ReactNode
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'danger'
  className?: string
}> = ({ children, variant = 'default', className = '' }) => (
  <span className={`payload-badge payload-badge--${variant} ${className}`}>
    {children}
  </span>
)

// PayloadCMS-compatible Tabs components
export const PayloadTabs: React.FC<{
  children: React.ReactNode
  defaultValue: string
  value?: string
  onValueChange?: (value: string) => void
  className?: string
}> = ({ children, defaultValue, value, onValueChange, className = '' }) => {
  const [activeTab, setActiveTab] = React.useState(value || defaultValue)

  const handleTabChange = (newValue: string) => {
    setActiveTab(newValue)
    onValueChange?.(newValue)
  }

  return (
    <div className={`payload-tabs ${className}`} data-active-tab={activeTab}>
      {React.Children.map(children, child => 
        React.isValidElement(child) 
          ? React.cloneElement(child, { activeTab, onTabChange: handleTabChange } as any)
          : child
      )}
    </div>
  )
}

export const PayloadTabsList: React.FC<{
  children: React.ReactNode
  className?: string
  activeTab?: string
  onTabChange?: (value: string) => void
}> = ({ children, className = '', activeTab, onTabChange }) => (
  <div className={`payload-tabs__list ${className}`}>
    {React.Children.map(children, child => 
      React.isValidElement(child) 
        ? React.cloneElement(child, { activeTab, onTabChange } as any)
        : child
    )}
  </div>
)

export const PayloadTabsTrigger: React.FC<{
  children: React.ReactNode
  value: string
  className?: string
  activeTab?: string
  onTabChange?: (value: string) => void
}> = ({ children, value, className = '', activeTab, onTabChange }) => (
  <button
    className={`payload-tabs__trigger ${activeTab === value ? 'payload-tabs__trigger--active' : ''} ${className}`}
    onClick={() => onTabChange?.(value)}
    type="button"
  >
    {children}
  </button>
)

export const PayloadTabsContent: React.FC<{
  children: React.ReactNode
  value: string
  className?: string
  activeTab?: string
}> = ({ children, value, className = '', activeTab }) => {
  if (activeTab !== value) return null
  
  return (
    <div className={`payload-tabs__content ${className}`}>
      {children}
    </div>
  )
}

// PayloadCMS-compatible Input component
export const PayloadInput: React.FC<{
  placeholder?: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  className?: string
  type?: string
}> = ({ placeholder, value, onChange, className = '', type = 'text' }) => (
  <input
    type={type}
    className={`payload-input ${className}`}
    placeholder={placeholder}
    value={value}
    onChange={onChange}
  />
)

// PayloadCMS-compatible Table components
export const PayloadTable: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <div className={`payload-table-wrapper ${className}`}>
    <table className="payload-table">
      {children}
    </table>
  </div>
)

export const PayloadTableHeader: React.FC<{
  children: React.ReactNode
}> = ({ children }) => (
  <thead className="payload-table__header">
    {children}
  </thead>
)

export const PayloadTableBody: React.FC<{
  children: React.ReactNode
}> = ({ children }) => (
  <tbody className="payload-table__body">
    {children}
  </tbody>
)

export const PayloadTableRow: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <tr className={`payload-table__row ${className}`}>
    {children}
  </tr>
)

export const PayloadTableHead: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <th className={`payload-table__head ${className}`}>
    {children}
  </th>
)

export const PayloadTableCell: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <td className={`payload-table__cell ${className}`}>
    {children}
  </td>
)

// PayloadCMS-compatible Grid components
export const PayloadGrid: React.FC<{
  children: React.ReactNode
  cols?: 1 | 2 | 3 | 4
  gap?: 'sm' | 'md' | 'lg'
  className?: string
}> = ({ children, cols = 1, gap = 'md', className = '' }) => (
  <div className={`payload-grid payload-grid--cols-${cols} payload-grid--gap-${gap} ${className}`}>
    {children}
  </div>
)

export const PayloadGridItem: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = '' }) => (
  <div className={`payload-grid__item ${className}`} style={{ overflow: 'visible' }}>
    {children}
  </div>
)
