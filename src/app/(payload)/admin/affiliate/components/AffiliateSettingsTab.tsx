'use client'

import React, { useState } from 'react'
import { Button } from '@payloadcms/ui'
import type { User, AffiliateSetting } from '@/payload-types'
import { Settings, Plus, Edit, Eye } from 'lucide-react'
import {
  PayloadCard,
  PayloadCardContent,
  PayloadCardDescription,
  PayloadCardHeader,
  PayloadCardTitle,
  PayloadBadge
} from './PayloadUIComponents'

interface Props {
  selectedUser: User
  userSettings: AffiliateSetting[]
}

const AffiliateSettingsTab: React.FC<Props> = ({
  selectedUser,
  userSettings,
}) => {
  const [expandedSetting, setExpandedSetting] = useState<number | null>(null)

  const toggleExpanded = (settingId: number) => {
    setExpandedSetting(expandedSetting === settingId ? null : settingId)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getEventTitle = (setting: AffiliateSetting) => {
    if (typeof setting.event === 'object' && setting.event?.title) {
      return setting.event.title
    }
    return 'Unknown Event'
  }



  return (
    <div>
      {/* Header */}
      <div className="payload-flex payload-flex--between payload-mb">
        <div>
          <h3 style={{
            fontSize: 'var(--font-size-h4)',
            fontWeight: 'var(--font-weight-medium)',
            margin: '0 0 calc(var(--base) / 4) 0'
          }}>
            Affiliate Settings
          </h3>
          <p style={{
            fontSize: 'var(--font-size-small)',
            color: 'var(--theme-elevation-600)',
            margin: 0
          }}>
            Manage affiliate program configurations for {selectedUser.email}
          </p>
        </div>
        <Button buttonStyle="primary" size="small">
          <div className="payload-flex payload-flex--gap">
            <Plus style={{ width: '16px', height: '16px' }} />
            Add New Setting
          </div>
        </Button>
      </div>

      {/* Settings List */}
      {userSettings.length > 0 ? (
        <div>
          {userSettings.map((setting) => (
            <PayloadCard key={setting.id} className="payload-mb">
              <PayloadCardHeader>
                <div className="payload-flex payload-flex--between">
                  <div className="payload-flex payload-flex--gap">
                    <Settings style={{ width: '20px', height: '20px', color: 'var(--theme-elevation-600)' }} />
                    <div>
                      <PayloadCardTitle>{setting.name}</PayloadCardTitle>
                      <PayloadCardDescription>
                        Event: {getEventTitle(setting)} • Created: {formatDate(setting.createdAt)}
                      </PayloadCardDescription>
                    </div>
                  </div>
                  <div className="payload-flex payload-flex--gap">
                    <PayloadBadge variant={setting.isActive ? "success" : "secondary"}>
                      {setting.isActive ? 'Active' : 'Inactive'}
                    </PayloadBadge>
                    <Button
                      buttonStyle="secondary"
                      size="small"
                      onClick={() => toggleExpanded(setting.id)}
                    >
                      <Eye style={{ width: '16px', height: '16px' }} />
                    </Button>
                    <Button buttonStyle="secondary" size="small">
                      <Edit style={{ width: '16px', height: '16px' }} />
                    </Button>
                  </div>
                </div>
              </PayloadCardHeader>

              {expandedSetting === setting.id && (
                <PayloadCardContent>
                  <div>
                    <p style={{
                      fontSize: 'var(--font-size-small)',
                      color: 'var(--theme-elevation-600)',
                      margin: 'var(--base) 0'
                    }}>
                      Status: {setting.isActive ? 'Active' : 'Inactive'} |
                      Event: {getEventTitle(setting)}
                    </p>

                    {setting.description && (
                      <div style={{
                        padding: 'var(--base)',
                        backgroundColor: 'var(--theme-elevation-50)',
                        borderRadius: 'var(--border-radius-s)',
                        marginBottom: 'var(--base)'
                      }}>
                        <strong>Description:</strong> {setting.description}
                      </div>
                    )}

                    <div style={{
                      fontSize: 'var(--font-size-small)',
                      color: 'var(--theme-elevation-600)',
                      borderTop: '1px solid var(--theme-elevation-200)',
                      paddingTop: 'calc(var(--base) / 2)',
                      marginTop: 'var(--base)'
                    }}>
                      Created: {new Date(setting.createdAt).toLocaleString()} |
                      Updated: {new Date(setting.updatedAt).toLocaleString()}
                    </div>
                  </div>
                </PayloadCardContent>
              )}
            </PayloadCard>
          ))}
        </div>
      ) : (
        <PayloadCard>
          <PayloadCardContent>
            <div className="payload-empty-state">
              <Settings />
              <h3>No Settings Found</h3>
              <p>
                This affiliate user does not have any settings configured yet.
                Create a new setting to get started.
              </p>
              <Button buttonStyle="primary" size="small">
                <div className="payload-flex payload-flex--gap">
                  <Plus style={{ width: '16px', height: '16px' }} />
                  Create First Setting
                </div>
              </Button>
            </div>
          </PayloadCardContent>
        </PayloadCard>
      )}
    </div>
  )
}

export default AffiliateSettingsTab
